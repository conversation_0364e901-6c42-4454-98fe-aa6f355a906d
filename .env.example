# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Test Credentials for E2E Testing
TEST_USERNAME=your_kma_username
TEST_PASSWORD=your_kma_password

# Alternative test credentials (if you have multiple accounts)
TEST_USERNAME_2=another_username
TEST_PASSWORD_2=another_password

# Application Configuration
NEXT_PUBLIC_APP_NAME=KMA Schedule
NEXT_PUBLIC_APP_VERSION=1.0.0

# Development Settings
NODE_ENV=development
PORT=3000

# Test Environment Settings
PLAYWRIGHT_BASE_URL=http://localhost:3000
PLAYWRIGHT_HEADLESS=false
PLAYWRIGHT_TIMEOUT=30000

# Optional: Test Data
TEST_SEMESTER=1_2025_2026
TEST_EXPECTED_SUBJECTS=3
